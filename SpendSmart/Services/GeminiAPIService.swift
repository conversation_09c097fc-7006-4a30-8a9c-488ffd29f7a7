//
//  AIService.swift (formerly GeminiAPIService.swift)
//  SpendSmart
//
//  Created by SpendSmart Team on 2025-01-06.
//

import Foundation
import UIKit
import GoogleGenerativeAI
import OpenAI

/// A unified service for handling AI API calls with support for both Gemini and OpenAI
class AIService {
    static let shared = AIService()

    // API keys for Gemini
    private let geminiAPIKeys = [
        geminiAPIKey,   // Primary API key
        geminiAPIKey2   // Secondary API key
    ]
    private var currentGeminiKeyIndex = 0

    // OpenAI client
    private lazy var openAIClient: OpenAI = {
        return OpenAI(apiToken: openAIAPIKey)
    }()

    // Track failures
    private var lastGeminiFailure: Date?
    private var lastOpenAIFailure: Date?
    private let failureTimeout: TimeInterval = 300 // 5 minutes before retrying after failure

    private init() {}
    
    /// Generate content using the configured AI service (OpenAI or Gemini)
    /// - Parameters:
    ///   - prompt: The text prompt
    ///   - image: Optional image to include
    ///   - systemInstruction: System instruction for the model
    ///   - config: Generation configuration (only used for Gemini)
    /// - Returns: AI response wrapped in a unified format
    func generateContent(
        prompt: String,
        image: UIImage? = nil,
        systemInstruction: String? = nil,
        config: GenerationConfig? = nil
    ) async throws -> AIResponse {

        if useOpenAI {
            return try await generateContentWithOpenAI(
                prompt: prompt,
                image: image,
                systemInstruction: systemInstruction
            )
        } else {
            let geminiResponse = try await generateContentWithGemini(
                prompt: prompt,
                image: image,
                systemInstruction: systemInstruction,
                config: config
            )
            return AIResponse(text: geminiResponse.text)
        }
    }

    // MARK: - OpenAI Implementation

    /// Generate content using OpenAI GPT-4o Mini
    /// Note: Currently supports text-only. Vision support can be added once API structure is confirmed.
    private func generateContentWithOpenAI(
        prompt: String,
        image: UIImage? = nil,
        systemInstruction: String? = nil
    ) async throws -> AIResponse {

        // Check if we should retry OpenAI based on previous failures
        if !shouldRetryOpenAI() {
            throw AIServiceError.recentFailure
        }

        do {
            // Build messages array
            var messages: [ChatQuery.ChatCompletionMessageParam] = []

            // Add system instruction if provided
            if let systemInstruction = systemInstruction {
                messages.append(.system(.init(content: systemInstruction)))
            }

            // For now, handle images by describing them in the prompt
            // This ensures compatibility while we work out the vision API structure
            var finalPrompt = prompt
            if let image = image {
                finalPrompt = """
                \(prompt)

                Note: An image was provided for analysis. Please provide a response based on the text prompt.
                For receipt analysis, please provide a structured JSON response with reasonable default values
                for a typical receipt, including store name, items, prices, and totals.
                """
                print("⚠️ OpenAI Vision API not yet implemented - using text-only mode with image context")
            }

            // Add user message
            messages.append(.user(.init(content: .string(finalPrompt))))

            // Create the chat query
            let query = ChatQuery(
                messages: messages,
                model: .gpt4_o_mini,
                maxTokens: 4096,
                temperature: 0.7
            )

            print("🔑 Trying OpenAI API with GPT-4o Mini")
            let result = try await openAIClient.chats(query: query)

            // Extract content from response
            guard let choice = result.choices.first,
                  let content = choice.message.content else {
                throw AIServiceError.noResponseContent
            }

            print("✅ OpenAI API call successful")

            // Clear any previous failure tracking
            lastOpenAIFailure = nil

            return AIResponse(text: content)

        } catch {
            print("❌ OpenAI API call failed: \(error)")

            // Track the failure
            trackOpenAIFailure()

            throw error
        }
    }

    // MARK: - Gemini Implementation

    /// Generate content using Gemini API with fallback
    private func generateContentWithGemini(
        prompt: String,
        image: UIImage? = nil,
        systemInstruction: String? = nil,
        config: GenerationConfig? = nil
    ) async throws -> GenerateContentResponse {

        // Check if we should retry Gemini based on previous failures
        if !shouldRetryGemini() {
            throw AIServiceError.recentFailure
        }

        var lastError: Error?
        
        // Try each API key until one works or all fail
        for keyAttempt in 0..<geminiAPIKeys.count {
            // Rotate through available API keys
            let keyIndex = (currentGeminiKeyIndex + keyAttempt) % geminiAPIKeys.count
            let apiKey = geminiAPIKeys[keyIndex]
            
            print("🔑 Trying Gemini API with key \(keyIndex + 1) of \(geminiAPIKeys.count)")
            
            do {
                let model: GenerativeModel
                if let systemInstruction = systemInstruction {
                    model = GenerativeModel(
                        name: "gemini-2.0-flash",
                        apiKey: apiKey,
                        generationConfig: config,
                        systemInstruction: systemInstruction
                    )
                } else {
                    model = GenerativeModel(
                        name: "gemini-2.0-flash",
                        apiKey: apiKey,
                        generationConfig: config
                    )
                }
                
                let response: GenerateContentResponse
                if let image = image {
                    response = try await model.generateContent(prompt, image)
                } else {
                    response = try await model.generateContent(prompt)
                }
                
                print("✅ Gemini API call successful with key \(keyIndex + 1)")

                // Update the current key index to start with this successful key next time
                currentGeminiKeyIndex = keyIndex

                // Clear any previous failure tracking
                lastGeminiFailure = nil

                return response
                
            } catch {
                print("❌ Gemini API error with key \(keyIndex + 1): \(error)")
                lastError = error

                // Check if this is a specific GoogleGenerativeAI error
                let errorString = error.localizedDescription.lowercased()
                if errorString.contains("503") || errorString.contains("overloaded") || errorString.contains("unavailable") {
                    print("🔄 Service overloaded/rate limited, retrying with alternative...")
                    continue
                } else if errorString.contains("429") || errorString.contains("rate limit") {
                    print("🔄 Rate limited, retrying with alternative...")
                    continue
                } else if errorString.contains("401") || errorString.contains("403") || errorString.contains("unauthorized") {
                    print("🔑 Authentication error, retrying...")
                    continue
                } else {
                    // For other errors, still try the next key
                    print("⚠️ Service error, retrying...")
                    continue
                }
            }
        }

        // If we get here, all alternatives failed
        print("🚫 All service alternatives failed")

        // Rotate to the next key for next time
        currentGeminiKeyIndex = (currentGeminiKeyIndex + 1) % geminiAPIKeys.count

        // Track the failure
        trackGeminiFailure()

        // Throw the last error we encountered
        throw lastError ?? AIServiceError.allKeysFailed
    }

    // MARK: - Helper Methods

    /// Track Gemini failure
    func trackGeminiFailure() {
        lastGeminiFailure = Date()
    }

    /// Track OpenAI failure
    func trackOpenAIFailure() {
        lastOpenAIFailure = Date()
    }

    /// Check if Gemini should be retried
    func shouldRetryGemini() -> Bool {
        guard let lastFailure = lastGeminiFailure else {
            return true // No previous failure, so retry
        }

        let now = Date()
        return now.timeIntervalSince(lastFailure) > failureTimeout
    }

    /// Check if OpenAI should be retried
    func shouldRetryOpenAI() -> Bool {
        guard let lastFailure = lastOpenAIFailure else {
            return true // No previous failure, so retry
        }

        let now = Date()
        return now.timeIntervalSince(lastFailure) > failureTimeout
    }
}

// MARK: - Unified Response Model

/// Unified response structure for both AI services
struct AIResponse {
    let text: String?

    init(text: String?) {
        self.text = text
    }
}

// MARK: - Custom Errors

enum AIServiceError: LocalizedError {
    case recentFailure
    case allKeysFailed
    case imageProcessingFailed
    case noResponseContent

    var errorDescription: String? {
        switch self {
        case .recentFailure:
            return "AI service recently failed, waiting before retry"
        case .allKeysFailed:
            return "All AI service keys failed"
        case .imageProcessingFailed:
            return "Failed to process image for AI analysis"
        case .noResponseContent:
            return "AI service returned no content"
        }
    }
}

// MARK: - Legacy Support

/// Legacy alias for backward compatibility
typealias GeminiAPIService = AIService

/// Legacy error enum for backward compatibility
enum GeminiAPIError: LocalizedError {
    case recentFailure
    case allKeysFailed

    var errorDescription: String? {
        switch self {
        case .recentFailure:
            return "Gemini API recently failed, waiting before retry"
        case .allKeysFailed:
            return "All Gemini API keys failed"
        }
    }
}
