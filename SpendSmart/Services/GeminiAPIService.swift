//
//  GeminiAPIService.swift
//  SpendSmart
//
//  Created by SpendSmart Team on 2025-01-06.
//

import Foundation
import UIKit
import GoogleGenerativeAI

/// A service for handling Gemini API calls with multiple API keys and fallback
class GeminiAPIService {
    static let shared = GeminiAPIService()
    
    // API keys for Gemini
    private let geminiAPIKeys = [
        geminiAPIKey,   // Primary API key
        geminiAPIKey2   // Secondary API key
    ]
    private var currentGeminiKeyIndex = 0
    
    // Track Gemini failures
    private var lastGeminiFailure: Date?
    private let failureTimeout: TimeInterval = 300 // 5 minutes before retrying after failure
    
    private init() {}
    
    /// Generate content using Gemini API with fallback
    /// - Parameters:
    ///   - prompt: The text prompt
    ///   - image: Optional image to include
    ///   - systemInstruction: System instruction for the model
    ///   - config: Generation configuration
    /// - Returns: Generated content response
    func generateContent(
        prompt: String,
        image: UIImage? = nil,
        systemInstruction: String? = nil,
        config: GenerationConfig? = nil
    ) async throws -> GenerateContentResponse {
        
        // Check if we should retry Gemini based on previous failures
        if !shouldRetryGemini() {
            throw GeminiAPIError.recentFailure
        }
        
        var lastError: Error?
        
        // Try each API key until one works or all fail
        for keyAttempt in 0..<geminiAPIKeys.count {
            // Rotate through available API keys
            let keyIndex = (currentGeminiKeyIndex + keyAttempt) % geminiAPIKeys.count
            let apiKey = geminiAPIKeys[keyIndex]
            
            print("🔑 Trying Gemini API with key \(keyIndex + 1) of \(geminiAPIKeys.count)")
            
            do {
                let model: GenerativeModel
                if let systemInstruction = systemInstruction {
                    model = GenerativeModel(
                        name: "gemini-2.0-flash",
                        apiKey: apiKey,
                        generationConfig: config,
                        systemInstruction: systemInstruction
                    )
                } else {
                    model = GenerativeModel(
                        name: "gemini-2.0-flash",
                        apiKey: apiKey,
                        generationConfig: config
                    )
                }
                
                let response: GenerateContentResponse
                if let image = image {
                    response = try await model.generateContent(prompt, image)
                } else {
                    response = try await model.generateContent(prompt)
                }
                
                print("✅ Gemini API call successful with key \(keyIndex + 1)")

                // Update the current key index to start with this successful key next time
                currentGeminiKeyIndex = keyIndex

                // Clear any previous failure tracking
                lastGeminiFailure = nil

                return response
                
            } catch {
                print("❌ Gemini API error with key \(keyIndex + 1): \(error)")
                lastError = error

                // Check if this is a specific GoogleGenerativeAI error
                let errorString = error.localizedDescription.lowercased()
                if errorString.contains("503") || errorString.contains("overloaded") || errorString.contains("unavailable") {
                    print("🔄 Service overloaded/rate limited, retrying with alternative...")
                    continue
                } else if errorString.contains("429") || errorString.contains("rate limit") {
                    print("🔄 Rate limited, retrying with alternative...")
                    continue
                } else if errorString.contains("401") || errorString.contains("403") || errorString.contains("unauthorized") {
                    print("🔑 Authentication error, retrying...")
                    continue
                } else {
                    // For other errors, still try the next key
                    print("⚠️ Service error, retrying...")
                    continue
                }
            }
        }

        // If we get here, all alternatives failed
        print("🚫 All service alternatives failed")

        // Rotate to the next key for next time
        currentGeminiKeyIndex = (currentGeminiKeyIndex + 1) % geminiAPIKeys.count

        // Track the failure
        trackGeminiFailure()

        // Throw the last error we encountered
        throw lastError ?? GeminiAPIError.allKeysFailed
    }

    // MARK: - Helper Methods

    /// Track Gemini failure
    func trackGeminiFailure() {
        lastGeminiFailure = Date()
    }

    /// Check if Gemini should be retried
    func shouldRetryGemini() -> Bool {
        guard let lastFailure = lastGeminiFailure else {
            return true // No previous failure, so retry
        }

        let now = Date()
        return now.timeIntervalSince(lastFailure) > failureTimeout
    }
}

// MARK: - Custom Errors

enum GeminiAPIError: LocalizedError {
    case recentFailure
    case allKeysFailed

    var errorDescription: String? {
        switch self {
        case .recentFailure:
            return "Gemini API recently failed, waiting before retry"
        case .allKeysFailed:
            return "All Gemini API keys failed"
        }
    }
}
